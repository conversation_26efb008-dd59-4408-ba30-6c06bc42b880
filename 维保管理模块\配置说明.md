# 维保管理模块配置说明

## 1. 页面路由配置

在新项目的 `pages.json` 中添加以下路由配置：

```json
{
  "pages": [
    {
      "path": "pages/inspection/index",
      "style": {
        "navigationBarTitleText": "巡检",
        "navigationStyle": "custom"
      }
    }
  ]
}
```

## 2. 主页面集成

在工单管理主页面中集成维保管理组件：

```javascript
// 在 pages/workOrderManager/index.vue 中
import maintManager from "./maintHome/maintManager/index.vue";
import maint from "./maintHome/maint/index.vue";

export default {
  components: {
    maintManager,
    maint,
  },
  data() {
    return {
      // 维保管理配置
      maintainManageMenuPermission: "mobile-menu-preserveManage",
      maintainManageStatisticsList: [],
      maintainManageBtnPermission: [
        {
          name: "待派单",
          status: "6",
          num: 0,
          route: "/pages/inspection/index",
          permissionName: "mobile-preserveManage-pendingOrders",
          iconUrl: "/static/images/home/<USER>"
        },
        {
          name: "已逾期", 
          status: "12",
          num: 0,
          route: "/pages/inspection/index",
          permissionName: "mobile-preserveManage-overdue",
          iconUrl: "/static/images/home/<USER>"
        },
        {
          name: "已退回",
          status: "8", 
          num: 0,
          route: "/pages/inspection/index",
          permissionName: "mobile-preserveManage-returned",
          iconUrl: "/static/images/home/<USER>"
        }
      ],
      
      // 我的维保配置
      myMaintainMenuPermission: "mobile-menu-myPreserve",
      myMaintainStatisticsList: [],
      myMaintainBtnPermission: [
        {
          name: "待执行",
          status: "7",
          num: 0,
          route: "/pages/inspection/index",
          permissionName: "mobile-myPreserve-pendingImplement",
          iconUrl: "/static/images/home/<USER>"
        },
        {
          name: "处理中",
          status: "10",
          num: 0,
          route: "/pages/inspection/index",
          permissionName: "mobile-myPreserve-processing",
          iconUrl: "/static/images/home/<USER>"
        },
        {
          name: "已逾期",
          status: "12",
          num: 0,
          route: "/pages/inspection/index",
          permissionName: "mobile-myPreserve-overdue",
          iconUrl: "/static/images/home/<USER>"
        }
      ]
    };
  }
};
```

## 3. 模板中使用组件

```html
<!-- 维保管理 -->
<maintManager
  :maintainManageBtnPermission="maintainManageBtnPermission"
  :maintainManageMenuPermission="maintainManageMenuPermission"
  v-if="permissionList.includes(maintainManageMenuPermission)"
></maintManager>

<!-- 我的维保 -->
<maint
  :myMaintainBtnPermission="myMaintainBtnPermission"
  :myMaintainMenuPermission="myMaintainMenuPermission"
  v-if="permissionList.includes(myMaintainMenuPermission)"
></maint>
```

## 4. 权限配置

确保用户权限列表中包含以下权限：

### 维保管理权限：
- `mobile-menu-preserveManage` - 维保管理菜单权限
- `mobile-preserveManage-pendingOrders` - 待派单权限
- `mobile-preserveManage-overdue` - 已逾期权限  
- `mobile-preserveManage-returned` - 已退回权限

### 我的维保权限：
- `mobile-menu-myPreserve` - 我的维保菜单权限
- `mobile-myPreserve-pendingImplement` - 待执行权限
- `mobile-myPreserve-processing` - 处理中权限
- `mobile-myPreserve-overdue` - 已逾期权限

## 5. 依赖组件

确保项目中已安装以下组件：

- uni-ui 组件库
- uni-card
- uni-grid
- uni-grid-item
- uni-badge
- uni-tag
- uni-nav-bar
- uni-search-bar

## 6. 静态资源

确保以下图片资源存在：

- `/static/images/home/<USER>
- `/static/images/home/<USER>
- `/static/images/home/<USER>
- `/static/images/home/<USER>
- `/static/images/home/<USER>
- `/static/images/home/<USER>
- `/static/images/home/<USER>
- `/static/images/inspection/group1.png` - 工单图标

## 7. API 配置

确保 `@/utils/request` 工具存在并正确配置了请求拦截器和响应拦截器。

## 8. 状态管理

确保 `@/store/modules/user.js` 存在并包含用户权限信息：

```javascript
// store/modules/user.js
export default {
  state: {
    permissions: [] // 用户权限列表
  }
}
```
