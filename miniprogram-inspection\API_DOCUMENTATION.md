# 巡检管理模块 API 接口文档

## 接口基础信息

### 基础配置
- **基础域名**: 需要根据实际环境配置
- **请求方式**: 主要使用GET和PUT方法
- **数据格式**: JSON格式
- **认证方式**: Token认证（通过请求头传递）

### 通用响应格式
```json
{
  "code": 0,           // 状态码，0表示成功
  "message": "success", // 响应消息
  "data": {}           // 响应数据
}
```

### 状态码说明
- **0**: 操作成功
- **401**: 未授权，需要重新登录
- **403**: 权限不足
- **500**: 服务器内部错误

## 1. 巡检基础接口 (inspection.js)

### 1.1 获取巡检工单列表
**接口名称**: `getInspectionInfo`
**请求方式**: GET
**接口地址**: `/devops/app/work-order/getByStatus`

**请求参数**:
```json
{
  "status": "10",      // 工单状态（可选）
  "pageNo": 1,         // 页码
  "pageSize": 10,      // 每页数量
  "keyword": "",       // 搜索关键词（可选）
  "startTime": "",     // 开始时间（可选）
  "endTime": ""        // 结束时间（可选）
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "123456",
        "workOrderName": "设备巡检001",
        "status": "10",
        "statusName": "待执行",
        "createTime": "2024-01-01 10:00:00",
        "executor": "张三",
        "location": "1号楼"
      }
    ],
    "total": 100
  }
}
```

### 1.2 获取巡检统计数据
**接口名称**: `getStatistics`
**请求方式**: GET
**接口地址**: `/devops/app/work-order/statistics`

**请求参数**: 无

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "pending": 5,      // 待执行数量
    "executing": 3,    // 执行中数量
    "completed": 20,   // 已完成数量
    "overdue": 2       // 逾期数量
  }
}
```

## 2. 巡检详情接口 (inspectionDetail.js)

### 2.1 获取工单详情
**接口名称**: `getWorkOrderDetail`
**请求方式**: GET
**接口地址**: `/devops/app/work-order/getWorkOrderDetail`

**请求参数**:
```json
{
  "workOrderId": "123456"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "123456",
    "workOrderName": "设备巡检001",
    "status": "10",
    "statusName": "待执行",
    "description": "定期设备巡检",
    "createTime": "2024-01-01 10:00:00",
    "executor": "张三",
    "executorId": "user001",
    "location": "1号楼",
    "priority": "高"
  }
}
```

### 2.2 获取工单巡检信息
**接口名称**: `getWorkOrderInspect`
**请求方式**: GET
**接口地址**: `/devops/app/work-order/getWorkOrderInspect`

**请求参数**:
```json
{
  "workOrderId": "123456"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "equipmentLocation": "1号楼1层",
      "workOrderInspectVOList": [
        {
          "id": "item001",
          "equipmentName": "空调设备",
          "equipmentCode": "AC001",
          "inspectItems": [
            {
              "id": "inspect001",
              "itemName": "温度检查",
              "standardValue": "25±2℃",
              "actualValue": "",
              "result": "",
              "remark": ""
            }
          ]
        }
      ]
    }
  ]
}
```

### 2.3 派发工单
**接口名称**: `assignment`
**请求方式**: GET
**接口地址**: `/devops/work-order/assignment`

**请求参数**:
```json
{
  "workOrderId": "123456",
  "executor": "user001",
  "executorName": "张三"
}
```

### 2.4 获取工单流程
**接口名称**: `getWorkOrderFlow`
**请求方式**: GET
**接口地址**: `/devops/work-order-flow/get`

**请求参数**:
```json
{
  "id": "123456"
}
```

### 2.5 退回到负责人
**接口名称**: `returnHeadPerson`
**请求方式**: GET
**接口地址**: `/devops/work-order/returnHeadPerson`

**请求参数**:
```json
{
  "id": "123456",
  "returnReason": "设备故障，无法执行"
}
```

### 2.6 重新派单
**接口名称**: `againAssignment`
**请求方式**: GET
**接口地址**: `/devops/work-order/againAssignment`

**请求参数**:
```json
{
  "workOrderId": "123456",
  "executor": "user002",
  "executorName": "李四"
}
```

### 2.7 开始执行
**接口名称**: `startExecution`
**请求方式**: GET
**接口地址**: `/devops/work-order/startExecution`

**请求参数**:
```json
{
  "id": "123456"
}
```

### 2.8 逾期执行
**接口名称**: `beoverdueExecution`
**请求方式**: GET
**接口地址**: `/devops/work-order/beoverdueExecution`

**请求参数**:
```json
{
  "id": "123456"
}
```

## 3. 巡检执行接口 (inspectionExecute.js)

### 3.1 获取设备分类
**接口名称**: `getDeviceClassfy`
**请求方式**: GET
**接口地址**: `/devops/equipment-classify/get`

**请求参数**:
```json
{
  "id": "classify001"
}
```

### 3.2 提交设备巡检
**接口名称**: `submitDevice`
**请求方式**: GET
**接口地址**: `/devops/work-order/submit`

**请求参数**:
```json
{
  "id": "123456"
}
```

### 3.3 更新工单项详情
**接口名称**: `updateWorkDetail`
**请求方式**: PUT
**接口地址**: `/devops/work-order-item-detail/update`

**请求参数**:
```json
{
  "id": "item001",
  "photoUrlList": ["photo1.jpg", "photo2.jpg"],
  "itemDetailEditReqVOList": [
    {
      "id": "detail001",
      "actualValue": "26℃",
      "result": "正常",
      "remark": "设备运行正常"
    }
  ]
}
```

### 3.4 更新巡检详情
**接口名称**: `updateInspectDetail`
**请求方式**: PUT
**接口地址**: `/devops/app/work-order/updateInspectDetail`
**请求头**: `Content-Type: application/json`

**请求参数**:
```json
[
  {
    "id": "inspect001",
    "actualValue": "26℃",
    "result": "正常",
    "remark": "设备运行正常",
    "photoUrls": ["photo1.jpg"]
  }
]
```

## 4. 任务详情接口 (taskDetail.js)

### 4.1 获取设备巡检详情
**接口名称**: `getEquipmentInspectDetailByHeadPerson`
**请求方式**: GET
**接口地址**: `/devops/app/work-order/getEquipmentInspectDetailByHeadPerson`

**请求参数**:
```json
{
  "workOrderId": "123456",
  "workOrderItemId": "item001"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "inspectDetailList": [
      {
        "id": "inspect001",
        "itemName": "温度检查",
        "standardValue": "25±2℃",
        "actualValue": "26℃",
        "result": "正常",
        "remark": "设备运行正常",
        "photoUrls": ["photo1.jpg"]
      }
    ]
  }
}
```

## 5. 系统字典接口 (system/dict.js)

### 5.1 获取字典数据
**接口名称**: `getInspectionTaskStatusDict`
**请求方式**: GET
**接口地址**: `/system/dict-data/type`
**基础API**: `/app-api`

**请求参数**:
```json
{
  "type": "Inspection_task_status"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "label": "待派单",
      "value": "0",
      "colorType": "info",
      "cssClass": ""
    },
    {
      "label": "待执行",
      "value": "10",
      "colorType": "warning",
      "cssClass": ""
    },
    {
      "label": "执行中",
      "value": "20",
      "colorType": "primary",
      "cssClass": ""
    },
    {
      "label": "已完成",
      "value": "30",
      "colorType": "success",
      "cssClass": ""
    }
  ]
}
```

## 6. 用户管理接口 (system/user.js)

### 6.1 用户密码重置
**接口名称**: `updateUserPwd`
**请求方式**: PUT
**接口地址**: `/system/user/profile/update-password`

**请求参数**:
```json
{
  "oldPassword": "123456",
  "newPassword": "newPassword123"
}
```

### 6.2 查询用户个人信息
**接口名称**: `getUserProfile`
**请求方式**: GET
**接口地址**: `/system/user/profile/get`

**请求参数**: 无

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "user001",
    "username": "zhangsan",
    "nickname": "张三",
    "email": "<EMAIL>",
    "mobile": "13800138000",
    "avatar": "avatar.jpg",
    "dept": {
      "id": "dept001",
      "name": "运维部"
    },
    "roles": [
      {
        "id": "role001",
        "name": "巡检员"
      }
    ]
  }
}
```

### 6.3 修改用户个人信息
**接口名称**: `updateUserProfile`
**请求方式**: PUT
**接口地址**: `/system/user/profile/update`

**请求参数**:
```json
{
  "nickname": "张三",
  "email": "<EMAIL>",
  "mobile": "13800138000"
}
```

### 6.4 用户头像上传
**接口名称**: `uploadAvatar`
**请求方式**: PUT
**接口地址**: `/system/user/profile/update-avatar`

**请求参数**:
```json
{
  "name": "avatar",
  "filePath": "/path/to/avatar.jpg"
}
```

### 6.5 获取巡检角色用户列表
**接口名称**: `getUserRoleIdListByRoleId`
**请求方式**: GET
**接口地址**: `/system/user/getUserRoleIdListByRoleId?roleId=155&name={name}`

**请求参数**:
- `name` (String): 用户姓名，用于搜索过滤

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": "user001",
      "username": "zhangsan",
      "nickname": "张三",
      "mobile": "13800138000",
      "dept": {
        "name": "运维部"
      }
    }
  ]
}
```

### 6.6 获取维保角色用户列表
**接口名称**: `getUserRoleIdListByRoleId2`
**请求方式**: GET
**接口地址**: `/system/user/getUserRoleIdListByRoleId?roleId=176&name={name}`

**请求参数**:
- `name` (String): 用户姓名，用于搜索过滤

**响应示例**: 同上

## 工单状态枚举

| 状态码 | 状态名称 | 描述 |
|--------|----------|------|
| 0 | 待派单 | 工单已创建，等待派发给执行人 |
| 10 | 待执行 | 工单已派发，等待执行人开始执行 |
| 20 | 执行中 | 执行人已开始执行工单 |
| 30 | 已完成 | 工单执行完成，等待验收 |
| 40 | 已关闭 | 工单已关闭 |
| 50 | 已退回 | 工单被退回，需要重新处理 |

## 接口调用注意事项

### 1. 认证和授权
- 所有接口都需要在请求头中携带认证token
- Token格式: `Authorization: Bearer {token}`
- Token过期时需要重新登录获取

### 2. 请求限制
- 单次请求超时时间: 10秒
- 文件上传大小限制: 10MB
- 并发请求数限制: 10个

### 3. 错误处理
- 网络错误: 检查网络连接和域名配置
- 401错误: Token过期，需要重新登录
- 403错误: 权限不足，联系管理员
- 500错误: 服务器错误，稍后重试

### 4. 数据格式
- 时间格式: `YYYY-MM-DD HH:mm:ss`
- 图片格式: 支持jpg、png、gif
- 文件路径: 使用相对路径

### 5. 小程序适配建议
- 使用wx.request替代uni.request
- 配置服务器域名白名单
- 实现请求拦截器统一处理token
- 添加加载状态和错误提示
