# 巡检管理接口使用示例

## 1. 基础配置

### 1.1 配置请求工具
首先需要在小程序中配置请求工具，替换原有的uni-app请求方式：

```javascript
// utils/miniprogram-request.js 已提供完整的请求工具
const request = require('../../utils/miniprogram-request.js')

// 设置API基础域名（需要在小程序管理后台配置域名白名单）
request.config.baseURL = 'https://your-api-domain.com'
```

### 1.2 小程序域名配置
在微信小程序管理后台的"开发设置"中添加服务器域名：
- request合法域名：`https://your-api-domain.com`
- uploadFile合法域名：`https://your-api-domain.com`

## 2. 页面中的接口调用示例

### 2.1 巡检列表页面 (pages/inspection/index.js)

```javascript
// pages/inspection/index.js
const request = require('../../utils/miniprogram-request.js')

Page({
  data: {
    inspectionList: [],
    loading: false,
    status: '10', // 当前筛选状态
    pageNo: 1,
    pageSize: 10
  },

  onLoad() {
    this.getInspectionList()
  },

  // 获取巡检列表
  getInspectionList() {
    this.setData({ loading: true })
    
    const params = {
      status: this.data.status,
      pageNo: this.data.pageNo,
      pageSize: this.data.pageSize
    }

    request.request({
      url: '/devops/app/work-order/getByStatus',
      method: 'GET',
      data: params
    }).then(res => {
      this.setData({
        inspectionList: res.data.list || [],
        loading: false
      })
    }).catch(err => {
      this.setData({ loading: false })
      console.error('获取巡检列表失败:', err)
    })
  },

  // 状态筛选
  onStatusChange(e) {
    const status = e.currentTarget.dataset.status
    this.setData({ 
      status: status,
      pageNo: 1 
    })
    this.getInspectionList()
  },

  // 跳转到详情页
  goToDetail(e) {
    const workOrderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/inspectionDetail/index?workOrderId=${workOrderId}`
    })
  }
})
```

### 2.2 巡检详情页面 (pages/inspectionDetail/index.js)

```javascript
// pages/inspectionDetail/index.js
const request = require('../../utils/miniprogram-request.js')

Page({
  data: {
    workOrderId: '',
    workOrderDetail: {},
    workOrderInspect: [],
    userList: []
  },

  onLoad(options) {
    this.setData({ workOrderId: options.workOrderId })
    this.getWorkOrderDetail()
    this.getWorkOrderInspect()
  },

  // 获取工单详情
  getWorkOrderDetail() {
    request.request({
      url: '/devops/app/work-order/getWorkOrderDetail',
      method: 'GET',
      data: { workOrderId: this.data.workOrderId }
    }).then(res => {
      this.setData({ workOrderDetail: res.data })
    }).catch(err => {
      console.error('获取工单详情失败:', err)
    })
  },

  // 获取工单巡检信息
  getWorkOrderInspect() {
    request.request({
      url: '/devops/app/work-order/getWorkOrderInspect',
      method: 'GET',
      data: { workOrderId: this.data.workOrderId }
    }).then(res => {
      this.setData({ workOrderInspect: res.data || [] })
    }).catch(err => {
      console.error('获取巡检信息失败:', err)
    })
  },

  // 派单操作
  onAssignment() {
    // 先获取用户列表
    this.getUserList()
  },

  // 获取用户列表
  getUserList() {
    request.request({
      url: '/system/user/getUserRoleIdListByRoleId?roleId=155&name=',
      method: 'GET'
    }).then(res => {
      this.setData({ userList: res.data || [] })
      this.showUserPicker()
    }).catch(err => {
      console.error('获取用户列表失败:', err)
    })
  },

  // 显示用户选择器
  showUserPicker() {
    const userNames = this.data.userList.map(user => user.nickname)
    wx.showActionSheet({
      itemList: userNames,
      success: (res) => {
        const selectedUser = this.data.userList[res.tapIndex]
        this.assignToUser(selectedUser)
      }
    })
  },

  // 派单给指定用户
  assignToUser(user) {
    wx.showLoading({ title: '派单中...' })
    
    request.request({
      url: '/devops/work-order/assignment',
      method: 'GET',
      data: {
        workOrderId: this.data.workOrderId,
        executor: user.id,
        executorName: user.nickname
      }
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '派单成功',
        icon: 'success'
      })
      this.getWorkOrderDetail() // 刷新详情
    }).catch(err => {
      wx.hideLoading()
      console.error('派单失败:', err)
    })
  },

  // 开始执行
  onStartExecution() {
    wx.showModal({
      title: '确认',
      content: '确定开始执行此工单吗？',
      success: (res) => {
        if (res.confirm) {
          this.startExecution()
        }
      }
    })
  },

  startExecution() {
    wx.showLoading({ title: '处理中...' })
    
    request.request({
      url: '/devops/work-order/startExecution',
      method: 'GET',
      data: { id: this.data.workOrderId }
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '开始执行成功',
        icon: 'success'
      })
      this.getWorkOrderDetail()
    }).catch(err => {
      wx.hideLoading()
      console.error('开始执行失败:', err)
    })
  },

  // 跳转到执行页面
  goToExecute(e) {
    const item = e.currentTarget.dataset.item
    const itemData = encodeURIComponent(JSON.stringify(item))
    wx.navigateTo({
      url: `/pages/inspectionExecute/index?item=${itemData}`
    })
  }
})
```

### 2.3 巡检执行页面 (pages/inspectionExecute/index.js)

```javascript
// pages/inspectionExecute/index.js
const request = require('../../utils/miniprogram-request.js')

Page({
  data: {
    deviceInfo: {},
    inspectDetailList: [],
    photoList: []
  },

  onLoad(options) {
    const item = JSON.parse(decodeURIComponent(options.item))
    this.setData({ deviceInfo: item })
    this.getInspectDetail()
  },

  // 获取巡检详情
  getInspectDetail() {
    request.request({
      url: '/devops/app/work-order/getEquipmentInspectDetailByHeadPerson',
      method: 'GET',
      data: {
        workOrderId: this.data.deviceInfo.workOrderId,
        workOrderItemId: this.data.deviceInfo.id
      }
    }).then(res => {
      this.setData({ 
        inspectDetailList: res.data.inspectDetailList || [] 
      })
    }).catch(err => {
      console.error('获取巡检详情失败:', err)
    })
  },

  // 输入实际值
  onInputActualValue(e) {
    const { index } = e.currentTarget.dataset
    const value = e.detail.value
    const inspectDetailList = this.data.inspectDetailList
    inspectDetailList[index].actualValue = value
    this.setData({ inspectDetailList })
  },

  // 选择结果
  onResultChange(e) {
    const { index } = e.currentTarget.dataset
    const value = e.detail.value
    const results = ['正常', '异常', '待检']
    const inspectDetailList = this.data.inspectDetailList
    inspectDetailList[index].result = results[value]
    this.setData({ inspectDetailList })
  },

  // 拍照
  onTakePhoto(e) {
    const { index } = e.currentTarget.dataset
    
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera', 'album'],
      success: (res) => {
        this.uploadPhoto(res.tempFilePaths[0], index)
      }
    })
  },

  // 上传照片
  uploadPhoto(filePath, index) {
    wx.showLoading({ title: '上传中...' })
    
    request.upload({
      url: '/system/user/profile/update-avatar', // 替换为实际的图片上传接口
      filePath: filePath,
      name: 'file'
    }).then(res => {
      wx.hideLoading()
      const inspectDetailList = this.data.inspectDetailList
      if (!inspectDetailList[index].photoUrls) {
        inspectDetailList[index].photoUrls = []
      }
      inspectDetailList[index].photoUrls.push(res.data.url)
      this.setData({ inspectDetailList })
      
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      })
    }).catch(err => {
      wx.hideLoading()
      console.error('上传失败:', err)
    })
  },

  // 提交巡检结果
  onSubmit() {
    // 验证必填项
    const inspectDetailList = this.data.inspectDetailList
    for (let item of inspectDetailList) {
      if (!item.actualValue || !item.result) {
        wx.showToast({
          title: '请完善巡检信息',
          icon: 'none'
        })
        return
      }
    }

    wx.showLoading({ title: '提交中...' })
    
    // 更新巡检详情
    request.request({
      url: '/devops/app/work-order/updateInspectDetail',
      method: 'PUT',
      data: inspectDetailList,
      header: {
        'Content-Type': 'application/json'
      }
    }).then(res => {
      // 提交工单
      return request.request({
        url: '/devops/work-order/submit',
        method: 'GET',
        data: { id: this.data.deviceInfo.workOrderId }
      })
    }).then(res => {
      wx.hideLoading()
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      })
      
      // 跳转到成功页面
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/inspectionSuccess/index'
        })
      }, 1500)
    }).catch(err => {
      wx.hideLoading()
      console.error('提交失败:', err)
    })
  }
})
```

## 3. 统计数据获取示例

```javascript
// 在首页或统计页面中获取统计数据
getStatistics() {
  request.request({
    url: '/devops/app/work-order/statistics',
    method: 'GET'
  }).then(res => {
    this.setData({
      statistics: {
        pending: res.data.pending || 0,
        executing: res.data.executing || 0,
        completed: res.data.completed || 0,
        overdue: res.data.overdue || 0
      }
    })
  }).catch(err => {
    console.error('获取统计数据失败:', err)
  })
}
```

## 4. 错误处理最佳实践

```javascript
// 统一错误处理
function handleApiError(err, defaultMessage = '操作失败') {
  console.error('API错误:', err)
  
  let message = defaultMessage
  if (err && err.message) {
    message = err.message
  }
  
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
}

// 使用示例
request.request({
  url: '/api/example',
  method: 'GET'
}).then(res => {
  // 成功处理
}).catch(err => {
  handleApiError(err, '获取数据失败')
})
```

## 5. 注意事项

1. **域名配置**: 确保在小程序管理后台配置了正确的服务器域名
2. **Token管理**: 登录后要保存token，请求时自动添加到请求头
3. **错误处理**: 统一处理401错误，自动跳转到登录页
4. **加载状态**: 在请求过程中显示加载状态，提升用户体验
5. **数据验证**: 提交前验证必填字段，避免无效请求
6. **图片上传**: 注意图片大小限制和格式要求
