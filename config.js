// 应用全局配置
module.exports = {
  // baseUrl: 'http://api-dashboard.yudao.iocoder.cn',
  // baseUrl: "http://192.168.15.18:48080",
  baseUrl: "http://192.168.15.18:48080",
  // baseUrlUpload:'http://10.10.254.120:48080/admin-api/infra/file/upload',
  //  baseUrl: 'http://192.168.15.18:48080',
  // baseUrl_Upload: 'http://10.10.254.120:48080/admin-api/infra/file/upload',
  // baseUrl_Upload: 'http://192.168.15.18:48080/admin-api/infra/file/upload',
  baseUrl_Upload: "http://192.168.15.18:48080/admin-api/infra/file/upload",
  // baseUrl: 'http://10.10.254.120:48080',
  // http://10.10.254.120:48080
  baseApi: "/admin-api",
  // 应用信息
  appInfo: {
    // 应用名称
    name: "yudao-app",
    // 应用版本
    version: "1.0.0",
    // 应用logo
    logo: "/static/Logo.png",
    // 官方网站
    site_url: "https://iocoder.cn",
    // 政策协议
    agreements: [
      {
        title: "隐私政策",
        url: "https://iocoder.cn",
      },
      {
        title: "用户服务协议",
        url: "https://iocoder.cn",
      },
    ],
  },
};
