<template>
  <view>
    <!-- 维保管理 -->
    <maintManager
      :maintainManageBtnPermission="maintainManageBtnPermission"
      :maintainManageMenuPermission="maintainManageMenuPermission"
      v-if="permissionList.includes(maintainManageMenuPermission)"
    ></maintManager>
    
    <!-- 我的维保 -->
    <maint
      :myMaintainBtnPermission="myMaintainBtnPermission"
      :myMaintainMenuPermission="myMaintainMenuPermission"
      v-if="permissionList.includes(myMaintainMenuPermission)"
    ></maint>
  </view>
</template>

<script>
  import store from "@/store/modules/user.js";
  import maintManager from "./maintHome/maintManager/index.vue";
  import maint from "./maintHome/maint/index.vue";
  
  export default {
    components: {
      maintManager,
      maint,
    },
    data() {
      return {
        permissionList: [],
        
        // 维保管理配置
        maintainManageMenuPermission: "mobile-menu-preserveManage",
        maintainManageStatisticsList: [],
        maintainManageBtnPermission: [
          {
            name: "待派单",
            status: "6",
            num: 0,
            route: "/pages/inspection/index",
            permissionName: "mobile-preserveManage-pendingOrders",
            iconUrl: "/static/images/home/<USER>"
          },
          {
            name: "已逾期", 
            status: "12",
            num: 0,
            route: "/pages/inspection/index",
            permissionName: "mobile-preserveManage-overdue",
            iconUrl: "/static/images/home/<USER>"
          },
          {
            name: "已退回",
            status: "8", 
            num: 0,
            route: "/pages/inspection/index",
            permissionName: "mobile-preserveManage-returned",
            iconUrl: "/static/images/home/<USER>"
          }
        ],
        
        // 我的维保配置
        myMaintainMenuPermission: "mobile-menu-myPreserve",
        myMaintainStatisticsList: [],
        myMaintainBtnPermission: [
          {
            name: "待执行",
            status: "7",
            num: 0,
            route: "/pages/inspection/index",
            permissionName: "mobile-myPreserve-pendingImplement",
            iconUrl: "/static/images/home/<USER>"
          },
          {
            name: "处理中",
            status: "10",
            num: 0,
            route: "/pages/inspection/index",
            permissionName: "mobile-myPreserve-processing",
            iconUrl: "/static/images/home/<USER>"
          },
          {
            name: "已逾期",
            status: "12",
            num: 0,
            route: "/pages/inspection/index",
            permissionName: "mobile-myPreserve-overdue",
            iconUrl: "/static/images/home/<USER>"
          }
        ]
      };
    },
    mounted() {
      this.permissionList = store.state.permissions;
    },
  };
</script>

<style>
  /* 根据需要添加样式 */
</style>
