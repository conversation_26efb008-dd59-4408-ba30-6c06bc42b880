# 维保管理模块

这个文件夹包含了完整的维保管理功能模块，可以直接用于最新版本的小程序。

## 目录结构

```
维保管理模块/
├── pages/                          # 页面文件
│   ├── maintHome/                 # 维保首页组件
│   │   ├── maintManager/          # 维保管理组件
│   │   │   └── index.vue         # 维保管理主组件
│   │   └── maint/                # 我的维保组件
│   │       └── index.vue         # 我的维保主组件
│   └── inspection/               # 巡检页面（维保管理使用）
│       └── index.vue             # 巡检列表页面
├── api/                          # API接口
│   ├── inspection/               # 巡检相关API
│   │   └── inspection.js         # 巡检基础接口
│   ├── inspectionDetail/         # 巡检详情API
│   │   └── inspectionDetail.js   # 巡检详情接口
│   └── system/                   # 系统相关API
│       └── dict.js               # 字典接口
├── 配置说明.md                    # 配置说明文档
├── 使用示例.vue                   # 使用示例代码
├── API文档.md                     # API接口文档
└── README.md                     # 项目说明文档

```

## 文件说明

### 核心页面文件
- `pages/maintHome/maintManager/index.vue` - 维保管理主组件，显示待派单、已逾期、已退回三个功能入口
- `pages/maintHome/maint/index.vue` - 我的维保主组件，显示待执行、处理中、已逾期三个功能入口
- `pages/inspection/index.vue` - 巡检列表页面，维保管理的三个功能都使用这个页面展示数据

### API接口文件
- `api/inspection/inspection.js` - 获取工单列表和统计信息
- `api/inspectionDetail/inspectionDetail.js` - 工单详情、派单、退回等操作
- `api/system/dict.js` - 获取字典数据（状态、类型等）

### 文档文件
- `配置说明.md` - 详细的配置步骤和集成说明
- `使用示例.vue` - 完整的使用示例代码
- `API文档.md` - 完整的API接口文档

## 功能说明

### 维保管理主要功能：
1. **待派单** - 显示等待派发的维保工单
2. **已逾期** - 显示已经逾期的维保工单  
3. **已退回** - 显示被退回的维保工单

### 我的维保主要功能：
1. **待执行** - 显示待执行的维保任务
2. **处理中** - 显示正在处理的维保任务
3. **已逾期** - 显示已逾期的维保任务

## 页面路由配置

在 `pages.json` 中需要添加以下路由配置：

```json
{
  "path": "pages/inspection/index",
  "style": {
    "navigationBarTitleText": "巡检",
    "navigationStyle": "custom"
  }
}
```

## 使用说明

1. 将此文件夹中的所有文件复制到新的小程序项目中
2. 确保 `pages.json` 中包含了必要的路由配置
3. 确保项目中有必要的依赖组件（如 uni-ui 组件）
4. 根据实际API地址调整接口配置

## 权限配置

维保管理模块使用以下权限标识：
- `mobile-menu-preserveManage` - 维保管理菜单权限
- `mobile-preserveManage-pendingOrders` - 待派单权限
- `mobile-preserveManage-overdue` - 已逾期权限  
- `mobile-preserveManage-returned` - 已退回权限

我的维保模块使用以下权限标识：
- `mobile-menu-myPreserve` - 我的维保菜单权限
- `mobile-myPreserve-pendingImplement` - 待执行权限
- `mobile-myPreserve-processing` - 处理中权限
- `mobile-myPreserve-overdue` - 已逾期权限

## 状态码说明

- **6** - 待派单
- **8** - 已退回  
- **12** - 已逾期
- **7** - 待执行
- **10** - 处理中

## 注意事项

1. 维保管理的三个页面（待派单、已逾期、已退回）都使用同一个页面文件 `pages/inspection/index.vue`
2. 通过不同的状态参数来区分显示不同状态的数据
3. 确保API接口地址与后端服务保持一致
4. 图片资源路径需要根据实际项目调整
