# 巡检管理模块

这个文件夹包含了从uni-app项目中提取的巡检管理相关的所有页面、API接口和组件代码，用于迁移到小程序项目中。

## 目录结构

```
miniprogram-inspection/
├── pages/                    # 页面文件
│   ├── inspection/           # 巡检列表页面
│   ├── inspectionDetail/     # 巡检详情页面
│   ├── inspectionExecute/    # 巡检执行页面
│   ├── inspectionHome/       # 巡检首页组件
│   ├── inspectionSuccess/    # 巡检成功页面
│   └── inspectorDetail/      # 巡检员详情页面
├── api/                      # API接口
│   ├── inspection/           # 巡检相关API
│   ├── inspectionDetail/     # 巡检详情API
│   ├── inspectionExecute/    # 巡检执行API
│   ├── taskDetail/           # 任务详情API
│   └── system/               # 系统相关API
├── components/               # 组件
│   └── topInfo/              # 顶部信息组件
├── utils/                    # 工具函数
├── static/                   # 静态资源
│   └── images/               # 图片资源
└── README.md                 # 说明文档
```

## 主要功能模块

### 1. 巡检列表 (inspection)
- 显示巡检工单列表
- 支持按状态筛选（待执行、执行中、已完成等）
- 支持搜索功能

### 2. 巡检详情 (inspectionDetail)
- 显示巡检工单详细信息
- 支持派单、退回、重新派单等操作
- 显示工单流程状态

### 3. 巡检执行 (inspectionExecute)
- 巡检任务执行界面
- 支持设备巡检项目填写
- 支持拍照上传
- 支持巡检结果提交

### 4. 巡检首页 (inspectionHome)
- 包含"我的巡检"和"巡检管理"两个组件
- 显示巡检统计信息
- 快速入口功能

### 5. 巡检成功 (inspectionSuccess)
- 巡检完成后的成功提示页面
- 返回首页功能

### 6. 巡检员详情 (inspectorDetail)
- 巡检员信息详情页面

## API接口详细说明

### 1. inspection.js - 巡检基础接口

#### getInspectionInfo(data)
- **功能**: 根据状态获取巡检工单信息
- **请求方式**: GET
- **接口地址**: `/devops/app/work-order/getByStatus`
- **参数**:
  - `data` (Object) - 查询参数，包含状态等筛选条件
- **返回**: 巡检工单列表数据

#### getStatistics()
- **功能**: 获取巡检统计数据
- **请求方式**: GET
- **接口地址**: `/devops/app/work-order/statistics`
- **参数**: 无
- **返回**: 巡检统计信息（待执行、执行中、已完成等数量）

### 2. inspectionDetail.js - 巡检详情接口

#### getWorkOrderDetail(workOrderId)
- **功能**: 获取工单详细信息
- **请求方式**: GET
- **接口地址**: `/devops/app/work-order/getWorkOrderDetail`
- **参数**:
  - `workOrderId` (String) - 工单ID
- **返回**: 工单详细信息

#### getWorkOrderInspect(workOrderId)
- **功能**: 获取工单巡检信息
- **请求方式**: GET
- **接口地址**: `/devops/app/work-order/getWorkOrderInspect`
- **参数**:
  - `workOrderId` (String) - 工单ID
- **返回**: 工单巡检详细信息，包含设备列表和巡检项目

#### assignment(workOrderId, executor, executorName)
- **功能**: 派发工单给执行人
- **请求方式**: GET
- **接口地址**: `/devops/work-order/assignment`
- **参数**:
  - `workOrderId` (String) - 工单ID
  - `executor` (String) - 执行人ID
  - `executorName` (String) - 执行人姓名
- **返回**: 派单结果

#### getWorkOrderFlow(workOrderId)
- **功能**: 获取工单流程图信息
- **请求方式**: GET
- **接口地址**: `/devops/work-order-flow/get`
- **参数**:
  - `workOrderId` (String) - 工单ID（作为id参数传递）
- **返回**: 工单流程状态信息

#### returnHeadPerson(id, returnReason)
- **功能**: 退回工单到负责人
- **请求方式**: GET
- **接口地址**: `/devops/work-order/returnHeadPerson`
- **参数**:
  - `id` (String) - 工单ID
  - `returnReason` (String) - 退回原因
- **返回**: 退回操作结果

#### againAssignment(workOrderId, executor, executorName)
- **功能**: 重新派发工单
- **请求方式**: GET
- **接口地址**: `/devops/work-order/againAssignment`
- **参数**:
  - `workOrderId` (String) - 工单ID
  - `executor` (String) - 执行人ID
  - `executorName` (String) - 执行人姓名
- **返回**: 重新派单结果

#### startExecution(workOrderId)
- **功能**: 开始执行工单
- **请求方式**: GET
- **接口地址**: `/devops/work-order/startExecution`
- **参数**:
  - `workOrderId` (String) - 工单ID（作为id参数传递）
- **返回**: 开始执行结果

#### beoverdueExecution(workOrderId)
- **功能**: 标记工单为逾期执行
- **请求方式**: GET
- **接口地址**: `/devops/work-order/beoverdueExecution`
- **参数**:
  - `workOrderId` (String) - 工单ID（作为id参数传递）
- **返回**: 逾期执行标记结果

### 3. inspectionExecute.js - 巡检执行接口

#### getDeviceClassfy(id)
- **功能**: 获取设备分类信息
- **请求方式**: GET
- **接口地址**: `/devops/equipment-classify/get`
- **参数**:
  - `id` (String) - 设备分类ID
- **返回**: 设备分类详细信息

#### submitDevice(id)
- **功能**: 提交设备巡检结果
- **请求方式**: GET
- **接口地址**: `/devops/work-order/submit`
- **参数**:
  - `id` (String) - 工单ID
- **返回**: 提交结果

#### updateWorkDetail(id, photoUrlList, itemDetailEditReqVOList)
- **功能**: 更新工单项详情
- **请求方式**: PUT
- **接口地址**: `/devops/work-order-item-detail/update`
- **参数**:
  - `id` (String) - 工单项ID
  - `photoUrlList` (Array) - 照片URL列表
  - `itemDetailEditReqVOList` (Array) - 工单项详情编辑请求列表
- **返回**: 更新结果

#### updateInspectDetail(updateInspectDetailReqVOs)
- **功能**: 更新巡检详情
- **请求方式**: PUT
- **接口地址**: `/devops/app/work-order/updateInspectDetail`
- **请求头**: `Content-Type: application/json`
- **参数**:
  - `updateInspectDetailReqVOs` (Array) - 巡检详情更新请求对象列表
- **返回**: 更新结果

### 4. taskDetail.js - 任务详情接口

#### getEquipmentInspectDetailByHeadPerson(workOrderId, workOrderItemId)
- **功能**: 根据负责人获取设备巡检详情
- **请求方式**: GET
- **接口地址**: `/devops/app/work-order/getEquipmentInspectDetailByHeadPerson`
- **参数**:
  - `workOrderId` (String) - 工单ID
  - `workOrderItemId` (String) - 工单项ID
- **返回**: 设备巡检详情信息

### 5. system/dict.js - 字典数据接口

#### getInspectionTaskStatusDict(type)
- **功能**: 获取巡检任务状态字典数据
- **请求方式**: GET
- **接口地址**: `/system/dict-data/type`
- **基础API**: `/app-api`
- **参数**:
  - `type` (String) - 字典类型
- **返回**: 字典数据列表

### 6. system/user.js - 用户相关接口

#### updateUserPwd(oldPassword, newPassword)
- **功能**: 用户密码重置
- **请求方式**: PUT
- **接口地址**: `/system/user/profile/update-password`
- **参数**:
  - `oldPassword` (String) - 旧密码
  - `newPassword` (String) - 新密码
- **返回**: 密码更新结果

#### getUserProfile()
- **功能**: 查询用户个人信息
- **请求方式**: GET
- **接口地址**: `/system/user/profile/get`
- **参数**: 无
- **返回**: 用户个人信息

#### updateUserProfile(data)
- **功能**: 修改用户个人信息
- **请求方式**: PUT
- **接口地址**: `/system/user/profile/update`
- **参数**:
  - `data` (Object) - 用户信息数据
- **返回**: 更新结果

#### uploadAvatar(data)
- **功能**: 用户头像上传
- **请求方式**: PUT
- **接口地址**: `/system/user/profile/update-avatar`
- **参数**:
  - `data` (Object) - 包含name和filePath的上传数据
- **返回**: 上传结果

#### getUserRoleIdListByRoleId(name)
- **功能**: 根据角色ID获取用户列表（巡检角色）
- **请求方式**: GET
- **接口地址**: `/system/user/getUserRoleIdListByRoleId?roleId=155&name={name}`
- **参数**:
  - `name` (String) - 用户姓名（可选，用于搜索）
- **返回**: 巡检角色用户列表

#### getUserRoleIdListByRoleId2(name)
- **功能**: 根据角色ID获取用户列表（维保角色）
- **请求方式**: GET
- **接口地址**: `/system/user/getUserRoleIdListByRoleId?roleId=176&name={name}`
- **参数**:
  - `name` (String) - 用户姓名（可选，用于搜索）
- **返回**: 维保角色用户列表

## 接口使用说明

### 接口基础配置
所有接口都通过 `utils/request.js` 进行统一请求处理，支持以下配置：
- **基础域名**: 需要在小程序中配置服务器域名白名单
- **请求头**: 自动添加认证token和Content-Type
- **拦截器**: 统一处理请求和响应，包括错误处理
- **超时设置**: 默认10秒超时

### 接口调用示例

```javascript
// 获取巡检列表
import { getInspectionInfo } from '@/api/inspection/inspection.js'

// 调用示例
const params = {
  status: '10', // 工单状态
  pageNo: 1,
  pageSize: 10
}
getInspectionInfo(params).then(res => {
  if (res.code === 0) {
    console.log('巡检列表:', res.data)
  }
})

// 获取工单详情
import { getWorkOrderDetail } from '@/api/inspectionDetail/inspectionDetail.js'

getWorkOrderDetail('workOrderId123').then(res => {
  if (res.code === 0) {
    console.log('工单详情:', res.data)
  }
})

// 提交巡检结果
import { updateInspectDetail } from '@/api/inspectionExecute/inspectionExecute.js'

const inspectData = [
  {
    id: 'inspectId1',
    result: '正常',
    remark: '设备运行正常',
    photoUrls: ['photo1.jpg', 'photo2.jpg']
  }
]
updateInspectDetail(inspectData).then(res => {
  if (res.code === 0) {
    console.log('提交成功')
  }
})
```

### 接口状态码说明
- **0**: 成功
- **401**: 未授权，需要重新登录
- **403**: 权限不足
- **500**: 服务器内部错误

### 工单状态说明
- **0**: 待派单
- **10**: 待执行
- **20**: 执行中
- **30**: 已完成
- **40**: 已关闭
- **50**: 已退回

## 迁移到小程序注意事项

### 1. 接口适配
- **域名配置**: 需要在小程序管理后台配置服务器域名白名单
- **请求方式**: 将uni.request改为wx.request
- **请求拦截**: 重新实现请求和响应拦截器
- **错误处理**: 适配小程序的错误处理机制

### 2. 页面路由调整
- uni-app的路由需要改为小程序的路由方式
- 页面跳转方法需要调整

### 3. 组件引用
- uni-app的组件需要改为小程序原生组件或自定义组件
- uview-ui组件需要替换为小程序兼容的组件

### 4. API请求
- 请求方法需要改为小程序的wx.request
- 请求拦截器需要重新实现

### 5. 样式调整
- scss样式需要转换为小程序支持的样式
- 部分CSS属性可能需要调整

### 6. 生命周期
- uni-app的生命周期需要改为小程序的生命周期
- onLoad、onShow等方法需要调整

### 7. 权限管理
- 小程序的权限管理机制需要重新实现
- 用户认证和授权流程需要调整

## 依赖的第三方库

- moment.js - 时间处理
- crypto-js - 加密处理
- thorui-uni - UI组件库（需要替换为小程序兼容版本）
- uview-ui - UI组件库（需要替换为小程序兼容版本）

## 使用建议

1. 建议先从简单的页面开始迁移，如inspectionSuccess页面
2. 逐步迁移复杂的功能页面，如inspectionExecute页面
3. 最后处理首页和列表页面的复杂交互
4. 建议保持原有的业务逻辑不变，只调整技术实现方式
