# 维保管理模块 API 接口文档

## 接口基础信息

### 基础配置
- **基础域名**: 需要根据实际环境配置
- **请求方式**: 主要使用GET方法
- **数据格式**: JSON格式
- **认证方式**: Token认证（通过请求头传递）

### 通用响应格式
```json
{
  "code": 0,           // 状态码，0表示成功
  "message": "success", // 响应消息
  "data": {}           // 响应数据
}
```

## 1. 巡检基础接口 (inspection.js)

### 1.1 获取巡检工单列表
**接口名称**: `getInspectionInfo`
**请求方式**: GET
**接口地址**: `/devops/app/work-order/getByStatus`

**请求参数**:
```json
{
  "pageNo": 1,                    // 页码
  "pageSize": 10,                 // 每页数量
  "permissionIdentification": "", // 权限标识
  "status": "",                   // 工单状态（可选）
  "workOrderName": ""             // 工单名称（可选）
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "工单ID",
        "workOrderName": "工单名称",
        "workOrderCode": "工单编号",
        "status": 6,
        "frequencyDate": "执行日期",
        "frequencyTime": "执行时间",
        "planType": "巡检类型"
      }
    ],
    "total": 100
  }
}
```

### 1.2 获取工单统计信息
**接口名称**: `getStatistics`
**请求方式**: GET
**接口地址**: `/devops/app/work-order/statistics`

**响应数据**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "permissionIdentification": "mobile-menu-preserveManage",
      "total": 50,
      "workOrderStatisticsList": [
        {
          "status": 6,
          "countTotal": 10
        },
        {
          "status": 12,
          "countTotal": 5
        }
      ]
    }
  ]
}
```

## 2. 巡检详情接口 (inspectionDetail.js)

### 2.1 获取工单详情
**接口名称**: `getWorkOrderDetail`
**请求方式**: GET
**接口地址**: `/devops/app/work-order/getWorkOrderDetail`

**请求参数**:
```json
{
  "workOrderId": "工单ID"
}
```

### 2.2 派单操作
**接口名称**: `assignment`
**请求方式**: GET
**接口地址**: `/devops/work-order/assignment`

**请求参数**:
```json
{
  "workOrderId": "工单ID",
  "executor": "执行人ID",
  "executorName": "执行人姓名"
}
```

### 2.3 退回到负责人
**接口名称**: `returnHeadPerson`
**请求方式**: GET
**接口地址**: `/devops/work-order/returnHeadPerson`

**请求参数**:
```json
{
  "id": "工单ID",
  "returnReason": "退回原因"
}
```

### 2.4 重新派单
**接口名称**: `againAssignment`
**请求方式**: GET
**接口地址**: `/devops/work-order/againAssignment`

**请求参数**:
```json
{
  "workOrderId": "工单ID",
  "executor": "执行人ID",
  "executorName": "执行人姓名"
}
```

### 2.5 开始执行
**接口名称**: `startExecution`
**请求方式**: GET
**接口地址**: `/devops/work-order/startExecution`

**请求参数**:
```json
{
  "id": "工单ID"
}
```

### 2.6 逾期执行
**接口名称**: `beoverdueExecution`
**请求方式**: GET
**接口地址**: `/devops/work-order/beoverdueExecution`

**请求参数**:
```json
{
  "id": "工单ID"
}
```

## 3. 系统字典接口 (dict.js)

### 3.1 获取字典数据
**接口名称**: `getInspectionTaskStatusDict`
**请求方式**: GET
**接口地址**: `/system/dict-data/type`

**请求参数**:
```json
{
  "type": "字典类型" // 如: "Inspection_task_status", "Inspection_type"
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "value": "6",
      "label": "待派单"
    },
    {
      "value": "12",
      "label": "已逾期"
    }
  ]
}
```

## 状态码说明

### 工单状态码
- **6** - 待派单
- **7** - 待执行
- **8** - 已退回
- **10** - 处理中
- **12** - 已逾期

### 字典类型
- **Inspection_task_status** - 巡检任务状态
- **Inspection_type** - 巡检类型

## 错误处理

所有接口都遵循统一的错误响应格式：

```json
{
  "code": 500,
  "message": "错误信息",
  "data": null
}
```

常见错误码：
- **401** - 未授权，需要重新登录
- **403** - 权限不足
- **500** - 服务器内部错误
