/**
 * 小程序请求工具
 * 适配原有的uni-app请求方式到小程序wx.request
 */

// 基础配置
const config = {
  baseURL: 'https://your-api-domain.com', // 替换为实际的API域名
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
}

// 获取token
function getToken() {
  return wx.getStorageSync('token') || ''
}

// 设置token
function setToken(token) {
  wx.setStorageSync('token', token)
}

// 清除token
function clearToken() {
  wx.removeStorageSync('token')
}

// 请求拦截器
function requestInterceptor(options) {
  // 添加基础URL
  if (!options.url.startsWith('http')) {
    options.url = config.baseURL + options.url
  }
  
  // 添加请求头
  options.header = {
    ...config.header,
    ...options.header
  }
  
  // 添加token
  const token = getToken()
  if (token) {
    options.header.Authorization = `Bearer ${token}`
  }
  
  // 处理baseApi参数
  if (options.baseApi) {
    options.url = options.url.replace(config.baseURL, config.baseURL + options.baseApi)
  }
  
  return options
}

// 响应拦截器
function responseInterceptor(response, resolve, reject) {
  const { statusCode, data } = response
  
  if (statusCode === 200) {
    // 检查业务状态码
    if (data.code === 0) {
      resolve(data)
    } else if (data.code === 401) {
      // token过期，清除token并跳转到登录页
      clearToken()
      wx.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/index'
        })
      }, 1500)
      reject(data)
    } else {
      // 其他业务错误
      wx.showToast({
        title: data.message || '请求失败',
        icon: 'none'
      })
      reject(data)
    }
  } else {
    // HTTP状态码错误
    wx.showToast({
      title: `请求失败(${statusCode})`,
      icon: 'none'
    })
    reject(response)
  }
}

// 主要请求方法
function request(options) {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const requestOptions = requestInterceptor(options)
    
    // 发起请求
    wx.request({
      url: requestOptions.url,
      method: requestOptions.method || 'GET',
      data: requestOptions.data,
      header: requestOptions.header,
      timeout: config.timeout,
      success: (response) => {
        responseInterceptor(response, resolve, reject)
      },
      fail: (error) => {
        console.error('请求失败:', error)
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

// GET请求
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

// POST请求
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// PUT请求
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// DELETE请求
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}

// 文件上传
function upload(options) {
  return new Promise((resolve, reject) => {
    const token = getToken()
    const header = {
      ...options.header
    }
    
    if (token) {
      header.Authorization = `Bearer ${token}`
    }
    
    wx.uploadFile({
      url: config.baseURL + options.url,
      filePath: options.filePath,
      name: options.name || 'file',
      formData: options.formData || {},
      header,
      success: (response) => {
        try {
          const data = JSON.parse(response.data)
          if (data.code === 0) {
            resolve(data)
          } else {
            wx.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            })
            reject(data)
          }
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        console.error('上传失败:', error)
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

// 兼容原有的request方法
function compatibleRequest(options) {
  // 兼容原有的uni-app请求格式
  const method = options.method || 'GET'
  const data = options.data || {}
  
  return request({
    url: options.url,
    method: method,
    data: data,
    header: options.headers || options.header,
    baseApi: options.baseApi
  })
}

// 导出方法
module.exports = {
  request: compatibleRequest, // 兼容原有调用方式
  get,
  post,
  put,
  delete: del,
  upload,
  setToken,
  getToken,
  clearToken,
  config
}

// 使用示例：
/*
// 在页面中使用
const request = require('../../utils/miniprogram-request.js')

// 方式1：使用兼容的request方法（与原有代码保持一致）
request.request({
  url: '/devops/app/work-order/getByStatus',
  method: 'GET',
  data: { status: '10' }
}).then(res => {
  console.log(res.data)
})

// 方式2：使用新的便捷方法
request.get('/devops/app/work-order/getByStatus', { status: '10' })
  .then(res => {
    console.log(res.data)
  })

// 文件上传示例
request.upload({
  url: '/system/user/profile/update-avatar',
  filePath: tempFilePath,
  name: 'avatar'
}).then(res => {
  console.log('上传成功:', res.data)
})
*/
